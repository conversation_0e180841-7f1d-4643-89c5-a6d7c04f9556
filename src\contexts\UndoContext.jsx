import { createContext, useContext, useState } from "react";

export const UndoContext = createContext();

export function UndoProvider({ children }) {
  const [history, setHistory] = useState([]);
  const [lines, setLines] = useState([]);
  const [boxes, setBoxes] = useState([]);
  const [arrows, setArrows] = useState([]);
  const [doubleArrow, setDoubleArrow] = useState([]);

  const addAction = (action) => {
    setHistory((prev) => [...prev, action]);
  };

  const undo = () => {
    const last = history[history.length - 1];
    if (!last) return null;

    setHistory((prev) => prev.slice(0, -1));

    // Undo based on the action type
    switch (last.type) {
      case "add-line":
        setLines((prev) => prev.filter((line) => line.id !== last.payload.id));
        break;
      case "add-text":
        setBoxes((prev) => prev.filter((box) => box.id !== last.payload.id));
        break;
      case "add-arrow":
        setArrows((prev) =>
          prev.filter((arrow) => arrow.id !== last.payload.id)
        );
        break;
      case "add-doubleArrow":
        setDoubleArrow((prev) =>
          prev.filter((doubleArrow) => doubleArrow.id !== last.payload.id)
        );
        break;
      case "delete-shapes":
        // Restore deleted shape based on its type
        if (last.shapeType === "line") {
          // Restore line from JSON
          const lineData = JSON.parse(last.payload.json);
          setLines((prev) => [
            ...prev,
            {
              id: lineData.id,
              tool: lineData.attrs.tool || "solid",
              points: lineData.attrs.points,
            },
          ]);
        } else if (last.shapeType === "arrow") {
          // Restore arrow from JSON
          const arrowData = JSON.parse(last.payload.json);
          setArrows((prev) => [
            ...prev,
            { id: arrowData.id, tool: "arrow", points: arrowData.attrs.points },
          ]);
        } else if (last.shapeType === "double-arrow") {
          // Restore double arrow from JSON
          const doubleArrowData = JSON.parse(last.payload.json);
          setDoubleArrow((prev) => [
            ...prev,
            {
              id: doubleArrowData.id,
              tool: "doubleArrow",
              points: doubleArrowData.attrs.points,
            },
          ]);
        } else if (last.shapeType === "text") {
          // Restore text from JSON
          const textData = JSON.parse(last.payload.json);
          setBoxes((prev) => [
            ...prev,
            {
              id: textData.id,
              x: textData.attrs.x,
              y: textData.attrs.y,
              text: textData.attrs.text || "",
              fontSize: textData.attrs.fontSize || 18,
            },
          ]);
        }
        break;
      default:
        break;
    }

    return last;
  };

  return (
    <UndoContext.Provider
      value={{
        history,
        addAction,
        undo,
        lines,
        setLines,
        boxes,
        setBoxes,
        arrows,
        setArrows,
        doubleArrow,
        setDoubleArrow,
      }}
    >
      {children}
    </UndoContext.Provider>
  );
}

export function useUndo() {
  return useContext(UndoContext);
}
