import { useRef, useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Stage,
  Layer,
  Rect,
  Line,
  Text,
  Group,
  Circle,
  Arrow,
} from "react-konva";

// Import Components
import ToolBar from "../../components/DrawTools/Toolbar/Toolbar";
import {
  createSquareAndRoundHoles,
  createDimensionLinesOnly,
  TABLET_BREAKPOINT_MIN,
  TABLET_BREAKPOINT_MAX,
  DEFAULT_SQUARE_SIZE,
  TABLET_SQUARE_SIZE,
} from "../../components/DrawTools/Tools/SquareAndRoundHolesTool";
import DimensionTool from "../../components/DrawTools/Tools/DimensionTool";
import NumericKeypad from "../../components/DrawTools/Popups/NumericKeypad";
import FullScreenTool from "../../components/DrawTools/Tools/FullScreenTool";
import {
  createPatternOneTwo,
  updatePatternOneTwo,
  centerPatternOneTwo,
} from "../../components/DrawTools/Tools/PatternOneTwoTool";
import {
  createPatternThree,
  updatePatternThree,
  centerPatternThree,
} from "../../components/DrawTools/Tools/PatternThreeTool";

// Import custom hooks
import { useDimensionDetection } from "../../hooks/useDimensionDetection";

// React-icons
import { FaXmark } from "react-icons/fa6";
import { FaSave, FaRegCircle, FaRegSquare } from "react-icons/fa";
import { ImUndo } from "react-icons/im";
import { VscTriangleRight } from "react-icons/vsc";
import { TbLetterL } from "react-icons/tb";
import { SlOptionsVertical } from "react-icons/sl";
import {
  PatternIcon1_2,
  PatternIcon3,
  PatternIcon4,
  PatternIcon5,
  PatternIcon6,
  PatternIcon7,
} from "../../components/DrawTools/Icons/PatternIcons";
import LanguageSelector from "../../components/LanguageSelector";

// Add this import
import { useUndo } from "../../contexts/UndoContext";
import { FullscreenProvider } from "../../contexts/FullscreenContext";
import { useTool } from "../../contexts/ToolContext";
import { useTranslation } from "react-i18next";

const disabledKeys = [""];

const patternKeys = [
  "pattern12",
  "pattern3",
  "pattern4",
  "pattern5",
  "pattern6",
  "pattern7",
];

const iconMap = {
  pattern12: (
    <PatternIcon1_2 className="w-6 h-6 text-white group-hover:text-black" />
  ),
  pattern3: (
    <PatternIcon3 className="w-6 h-6 text-white group-hover:text-black" />
  ),
  pattern4: (
    <PatternIcon4 className="w-6 h-6 text-white group-hover:text-black" />
  ),
  pattern5: (
    <PatternIcon5 className="w-6 h-6 text-white group-hover:text-black" />
  ),
  pattern6: (
    <PatternIcon6 className="w-6 h-6 text-white group-hover:text-black" />
  ),
  pattern7: (
    <PatternIcon7 className="w-6 h-6 text-white group-hover:text-black" />
  ),
};

function DrawPage() {
  const navigate = useNavigate();
  const stageRef = useRef(null);
  const containerRef = useRef(null);
  const { t } = useTranslation();
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [showPopup, setShowPopup] = useState(true);
  const [showAdjustSection, setShowAdjustSection] = useState(true);
  const [showToolbar, setShowToolbar] = useState(false);
  const [shapes, setShapes] = useState([]);
  const [selectedDimension, setSelectedDimension] = useState(null);
  const [currentDimensionValue, setCurrentDimensionValue] = useState("");
  const [currentScreenWidth, setCurrentScreenWidth] = useState(
    window.innerWidth
  );
  const [showProjectInfo, setShowProjectInfo] = useState(false);
  const [selectedPattern, setSelectedPattern] = useState(null);
  const [dimensionValues, setDimensionValues] = useState({
    width: "",
    height: "",
  });
  const { showDashLines, activeTool } = useTool();
  const { undo } = useUndo();

  // Use our custom hook for dimension detection
  const { showKeypad, setShowKeypad } = useDimensionDetection(
    stageRef,
    shapes,
    setShapes,
    showToolbar // Pass the toolbar state
  );

  const handleClose = () => {
    navigate("/create-report");
  };

  const togglePopup = () => {
    setShowPopup(!showPopup);
  };

  const toggleToolbar = () => {
    if (showAdjustSection) {
      // Switching from adjust to toolbar - make dimensions non-editable and black
      setShapes((prevShapes) =>
        prevShapes.map((shape) => {
          if (shape.type === "dimension") {
            return {
              ...shape,
              editable: false,
              stroke: "black",
              isVisible: shape.dimensionId === "nai" ? false : shape.isVisible,
            };
          }
          if (shape.type === "circle") {
            return {
              ...shape,
              isCircleVisible: true,
            };
          }
          if (shape.type === "text_label") {
            return {
              ...shape,
              isVisible: true,
            };
          }
          return shape;
        })
      );

      setShowAdjustSection(false);
      setShowToolbar(true);
    } else {
      // Switching from toolbar to normal - make dimensions editable and blue
      if (!showToolbar) {
        setShapes((prevShapes) =>
          prevShapes.map((shape) => {
            if (shape.type === "dimension") {
              return {
                ...shape,
                editable: true,
                stroke: "#07a4fe",
                isVisible: shape.dimensionId === "nai" ? true : shape.isVisible,
              };
            }
            if (shape.type === "circle") {
              return {
                ...shape,
                isCircleVisible: false,
              };
            }
            if (shape.type === "text_label") {
              return {
                ...shape,
                isVisible: false,
              };
            }
            return shape;
          })
        );
      }

      setShowToolbar(!showToolbar);
    }
  };

  const handleCancel = () => {
    setShowPopup(false);
    setSelectedPattern(null);
  };

  const handleRoundHoleSubmit = (values) => {
    const width = window.innerWidth;
    const squareSize =
      width >= TABLET_BREAKPOINT_MIN && width <= TABLET_BREAKPOINT_MAX
        ? TABLET_SQUARE_SIZE
        : DEFAULT_SQUARE_SIZE;

    const centerX = dimensions.width / 2;
    const centerY = dimensions.height / 2;
    const x = centerX - squareSize / 2;
    const y = centerY - squareSize / 2;

    const isTabletScreen =
      width >= TABLET_BREAKPOINT_MIN && width <= TABLET_BREAKPOINT_MAX;
    const dimensionOffsetY = isTabletScreen ? 75 : 0;

    const baseTop = shapes.find(
      (s) => s.type === "dimension" && !s.isVertical
    )?.text;

    const baseRight = shapes.find(
      (s) => s.type === "dimension" && s.isVertical
    )?.text;

    const newLines = createDimensionLinesOnly(
      values,
      squareSize,
      x,
      y,
      stageRef,
      {
        baseTop: baseTop !== undefined ? baseTop : 200,
        baseRight: baseRight !== undefined ? baseRight : 200,
      },
      dimensionOffsetY,
      values.diameter
    );

    setShapes((prev) => [...prev, ...newLines]);
    setShowPopup(false);
  };

  const handlePatternSelection = (key) => {
    const shapeCreators = {
      pattern12: {
        create: createPatternOneTwo,
        center: centerPatternOneTwo,
      },
      pattern3: {
        create: createPatternThree,
        center: centerPatternThree,
      },
    };

    // Set the selected pattern
    setSelectedPattern(t(`DrawPage.${key}`));

    const config = shapeCreators[key];
    if (config) {
      let newShapes = config.create(dimensions, stageRef);
      newShapes = config.center(newShapes, stageRef);

      const enhancedShapes = newShapes.map((shape) =>
        shape.type === "dimension"
          ? { ...shape, editable: true, stroke: "#07a4fe" }
          : shape
      );

      setShapes((prev) => [...prev, ...enhancedShapes]);

      setTimeout(() => {
        const layer = stageRef.current?.findOne("Layer");
        layer?.batchDraw();
      }, 0);
    }

    setShowPopup(false);
    setShowAdjustSection(true);
  };

  const handleSquareAndRoundHoles = () => {
    // Set the pattern type for Square and Round Holes
    setSelectedPattern("Square and Round Holes");

    const newShapes = createSquareAndRoundHoles(dimensions, stageRef);

    // Initialize dimension values with the default values from createSquareAndRoundHoles
    const horizontalDim = newShapes.find(
      (shape) => shape.type === "dimension" && !shape.isVertical
    );
    const verticalDim = newShapes.find(
      (shape) => shape.type === "dimension" && shape.isVertical
    );
    setDimensionValues({
      width: parseFloat(horizontalDim?.text),
      height: parseFloat(verticalDim?.text),
    });

    const enhancedShapes = newShapes.map((shape) => {
      if (shape.type === "dimension") {
        return {
          ...shape,
          editable: true,
          stroke: "#07a4fe",
        };
      }
      return shape;
    });

    // Force a re-render after adding shapes
    setShapes((prev) => [...prev, ...enhancedShapes]);
    setShowPopup(false);

    // Show adjust section instead of toolbar
    setShowAdjustSection(true);

    // Force a stage update
    setTimeout(() => {
      if (stageRef.current) {
        const layer = stageRef.current.findOne("Layer");
        if (layer) {
          layer.batchDraw();
        }
      }
    }, 0);
  };

  // Add this function to handle dimension clicks
  const handleDimensionClick = (dimensionId) => {
    if (showToolbar) return;

    const dimension = shapes.find(
      (shape) => shape.id === dimensionId && shape.type === "dimension"
    );

    if (dimension && dimension.editable !== false) {
      setSelectedDimension(dimension);
      setShowKeypad(true);
    }
  };

  // Add this function to directly update dimensions
  const updateDimensionValue = (dimensionId, newValue) => {
    // Convert to number if possible
    const numericValue = !isNaN(parseFloat(newValue))
      ? parseFloat(newValue)
      : newValue;
    const textStr = numericValue.toString();
    const textLength = textStr.length;

    // Calculate appropriate offset based on text length
    const baseOffset = 30;
    const additionalOffset = textLength > 3 ? (textLength - 3) * 8 : 0;
    const textOffset = baseOffset + additionalOffset;

    // Check if this is a pattern dimension
    const dimension = shapes.find(
      (shape) => shape.id === dimensionId && shape.type === "dimension"
    );

    // Find if there's a polyline with linked dimensions
    const polyline = shapes.find(
      (shape) =>
        shape.type === "polyline" &&
        shape.linkedDimensions?.includes(dimension?.dimensionId)
    );

    if (polyline && dimension) {
      // Determine which pattern we're dealing with
      if (dimension.dimensionId?.startsWith("pattern3")) {
        // Use the special update function for Pattern 3 pattern
        let updatedShapes = updatePatternThree(
          shapes,
          dimensionId,
          numericValue
        );

        // Center the pattern after updating dimensions
        updatedShapes = centerPatternThree(updatedShapes, stageRef);

        setShapes(updatedShapes);
      } else {
        // Use the special update function for Pattern 1,2
        let updatedShapes = updatePatternOneTwo(
          shapes,
          dimensionId,
          numericValue
        );

        // Center the pattern after updating dimensions
        updatedShapes = centerPatternOneTwo(updatedShapes, stageRef);

        setShapes(updatedShapes);
      }

      // Force redraw immediately
      setTimeout(() => {
        if (stageRef.current) {
          const layer = stageRef.current.findOne("Layer");
          if (layer) {
            layer.batchDraw();
          }
        }
      }, 0);
    } else {
      // Use the regular update for other shapes
      setShapes((prevShapes) =>
        prevShapes.map((shape) => {
          if (shape.id === dimensionId && shape.type === "dimension") {
            return {
              ...shape,
              text: numericValue,
              textOffset,
            };
          }
          return shape;
        })
      );

      // Force redraw
      setTimeout(() => {
        if (stageRef.current) {
          const layer = stageRef.current.findOne("Layer");
          if (layer) {
            layer.batchDraw();
          }
        }
      }, 0);
    }
  };

  // Handle window resize
  useEffect(() => {
    const resize = () => {
      if (containerRef.current) {
        const { clientWidth, clientHeight } = containerRef.current;
        setDimensions({ width: clientWidth, height: clientHeight });
      }
    };
    resize();
    window.addEventListener("resize", resize);
    return () => window.removeEventListener("resize", resize);
  }, []);

  const HORIZONTAL_OFFSET = 60;
  const VERTICAL_OFFSET = 80;

  // Add this effect to handle screen size changes
  useEffect(() => {
    const handleResize = () => {
      setCurrentScreenWidth(window.innerWidth);

      // If we have shapes, update based on the shape type
      if (shapes.length > 0) {
        // Handle square shapes
        const squareShape = shapes.find((shape) => shape.type === "rect");

        if (squareShape) {
          const width = window.innerWidth;

          const isTabletScreen =
            width >= TABLET_BREAKPOINT_MIN && width <= TABLET_BREAKPOINT_MAX;

          const isMobileScreen = width < TABLET_BREAKPOINT_MIN;

          const newSquareSize = isTabletScreen
            ? TABLET_SQUARE_SIZE
            : DEFAULT_SQUARE_SIZE;

          if (squareShape.width !== newSquareSize) {
            updateSquareSize(newSquareSize);
          }

          const currentBaseTop = shapes.find(
            (s) => s.type === "dimension" && !s.isVertical
          )?.text;
          const currentBaseRight = shapes.find(
            (s) => s.type === "dimension" && s.isVertical
          )?.text;

          const baseValuesForResize = {
            baseTop: currentBaseTop !== undefined ? currentBaseTop : 200,
            baseRight: currentBaseRight !== undefined ? currentBaseRight : 200,
          };

          // Tablet view
          if (isTabletScreen) {
            const dimensionElements = shapes.filter(
              (shape) => shape.type === "lineWithLabel"
            );

            if (dimensionElements.length > 0 && stageRef.current) {
              const stage = stageRef.current;
              const centerX = stage.width() / 2;
              const centerY = stage.height() / 2;

              const offsetY = 75;
              const x = centerX - newSquareSize / 2;
              const y = centerY - newSquareSize / 2 - offsetY;

              const dimensions = {
                topDimension: dimensionElements.find(
                  (shape) => !shape.isVertical
                )?.textMain,
                rightDimension: dimensionElements.find(
                  (shape) => shape.isVertical
                )?.textMain,
              };

              const newDimensionLines = createDimensionLinesOnly(
                dimensions,
                newSquareSize,
                x,
                y,
                stageRef,
                baseValuesForResize
              );

              setShapes((prevShapes) => {
                const shapesWithoutDimensions = prevShapes.filter(
                  (shape) =>
                    shape.type !== "lineWithLabel" &&
                    shape.type !== "tickLine" &&
                    shape.type !== "circle" &&
                    shape.type !== "text" &&
                    shape.type !== "line"
                );
                return [...shapesWithoutDimensions, ...newDimensionLines];
              });
            }
          }

          // Desktop view
          if (!isTabletScreen) {
            const dimensionElements = shapes.filter(
              (shape) => shape.type === "lineWithLabel"
            );

            if (dimensionElements.length > 0 && stageRef.current) {
              const stage = stageRef.current;
              const centerX = stage.width() / 2;
              const centerY = stage.height() / 2;
              const x = centerX - newSquareSize / 2;
              const y = centerY - newSquareSize / 2;

              const dimensions = {
                topDimension: dimensionElements.find(
                  (shape) => !shape.isVertical
                )?.textMain,
                rightDimension: dimensionElements.find(
                  (shape) => shape.isVertical
                )?.textMain,
              };

              const newDimensionLines = createDimensionLinesOnly(
                dimensions,
                newSquareSize,
                x,
                y,
                stageRef,
                baseValuesForResize
              );

              setShapes((prevShapes) => {
                const shapesWithoutDimensions = prevShapes.filter(
                  (shape) =>
                    shape.type !== "lineWithLabel" &&
                    shape.type !== "tickLine" &&
                    shape.type !== "circle" &&
                    shape.type !== "text" &&
                    shape.type !== "line"
                );
                return [...shapesWithoutDimensions, ...newDimensionLines];
              });
            }
          }

          // Mobile view
          if (isMobileScreen) {
            const dimensionElements = shapes.filter(
              (shape) => shape.type === "lineWithLabel"
            );

            if (dimensionElements.length > 0 && stageRef.current) {
              const stage = stageRef.current;
              const centerX = stage.width() / 2;
              const centerY = stage.height() / 2;
              const x = centerX - newSquareSize / 2;
              const y = centerY - newSquareSize / 2;

              const dimensions = {
                topDimension: dimensionElements.find(
                  (shape) => !shape.isVertical
                )?.textMain,
                rightDimension: dimensionElements.find(
                  (shape) => shape.isVertical
                )?.textMain,
              };

              const newDimensionLines = createDimensionLinesOnly(
                dimensions,
                newSquareSize,
                x,
                y,
                stageRef,
                baseValuesForResize,
                70
              );

              setShapes((prevShapes) => {
                const shapesWithoutDimensions = prevShapes.filter(
                  (shape) =>
                    shape.type !== "lineWithLabel" &&
                    shape.type !== "tickLine" &&
                    shape.type !== "circle" &&
                    shape.type !== "text" &&
                    shape.type !== "line"
                );
                return [...shapesWithoutDimensions, ...newDimensionLines];
              });
            }
          }
        }

        // Handle Pattern 1,2 pattern
        const pattern12Polyline = shapes.find(
          (shape) => shape.type === "polyline" && shape.linkedDimensions
        );

        if (pattern12Polyline && stageRef.current) {
          // Center the Pattern pattern on resize
          const centeredShapes = centerPatternOneTwo(shapes, stageRef);
          setShapes(centeredShapes);
        }

        // Handle Pattern 3 pattern
        const pattern3Polylines = shapes.filter(
          (shape) =>
            shape.type === "polyline" &&
            shape.points?.length === 4 &&
            !shape.linkedDimensions
        );

        if (pattern3Polylines.length > 0 && stageRef.current) {
          // Center the Pattern 3 pattern on resize
          const centeredShapes = centerPatternThree(shapes, stageRef);
          setShapes(centeredShapes);
        }
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [shapes]);

  // Add this effect to handle window resize and reposition shapes
  useEffect(() => {
    if (shapes.length > 0 && stageRef.current) {
      const squareShape = shapes.find((shape) => shape.type === "rect");
      const horizontalDimension = shapes.find(
        (shape) => shape.type === "dimension" && !shape.isVertical
      );
      const verticalDimension = shapes.find(
        (shape) => shape.type === "dimension" && shape.isVertical
      );

      if (squareShape && horizontalDimension && verticalDimension) {
        const stage = stageRef.current;
        const stageWidth = stage.width();
        const stageHeight = stage.height();

        const verticalOffset =
          window.innerWidth > TABLET_BREAKPOINT_MAX ? stageHeight * 0.1 : 0;

        const viewportCenter = {
          x: stageWidth / 2 / stage.scaleX() - stage.x() / stage.scaleX(),
          y:
            (stageHeight / 2 + verticalOffset) / stage.scaleY() -
            stage.y() / stage.scaleY(),
        };

        const squareSize = squareShape.width;
        const newX = viewportCenter.x - squareSize / 2;
        const newY = viewportCenter.y - squareSize / 2;

        const updatedShapes = shapes.map((shape) => {
          if (shape.id === squareShape.id) {
            return {
              ...shape,
              x: newX,
              y: newY,
              width: squareSize,
              height: squareSize,
            };
          } else if (shape.id === horizontalDimension.id) {
            return {
              ...shape,
              from: [newX, newY + squareSize + HORIZONTAL_OFFSET],
              to: [newX + squareSize, newY + squareSize + HORIZONTAL_OFFSET],
            };
          } else if (shape.id === verticalDimension.id) {
            return {
              ...shape,
              from: [newX - VERTICAL_OFFSET, newY],
              to: [newX - VERTICAL_OFFSET, newY + squareSize],
            };
          }
          return shape;
        });

        setShapes(updatedShapes);
      }
    }
  }, [dimensions]);

  // Function to update square size and related dimensions
  const updateSquareSize = (newSize) => {
    setShapes((prevShapes) => {
      const squareIndex = prevShapes.findIndex(
        (shape) => shape.type === "rect"
      );
      if (squareIndex === -1) return prevShapes;

      const square = prevShapes[squareIndex];

      const centerX = square.x + square.width / 2;
      const centerY = square.y + square.height / 2;

      const newX = centerX - newSize / 2;
      const newY = centerY - newSize / 2;

      const updatedShapes = [...prevShapes];

      updatedShapes[squareIndex] = {
        ...square,
        x: newX,
        y: newY,
        width: newSize,
        height: newSize,
      };

      const horizontalDimIndex = updatedShapes.findIndex(
        (shape) => shape.type === "dimension" && !shape.isVertical
      );
      if (horizontalDimIndex !== -1) {
        updatedShapes[horizontalDimIndex] = {
          ...updatedShapes[horizontalDimIndex],
          from: [newX, newY + newSize + HORIZONTAL_OFFSET],
          to: [newX + newSize, newY + newSize + HORIZONTAL_OFFSET],
        };
      }

      const verticalDimIndex = updatedShapes.findIndex(
        (shape) => shape.type === "dimension" && shape.isVertical
      );
      if (verticalDimIndex !== -1) {
        updatedShapes[verticalDimIndex] = {
          ...updatedShapes[verticalDimIndex],
          from: [newX - VERTICAL_OFFSET, newY],
          to: [newX - VERTICAL_OFFSET, newY + newSize],
        };
      }

      return updatedShapes;
    });
  };

  // Function to save the image with a white background
  const handleSaveImage = () => {
    const stage = stageRef.current;
    if (!stage) return;

    // Get the stage as an image
    const dataURL = stage.toDataURL({ pixelRatio: 2 });
    const image = new Image();
    image.src = dataURL;

    image.onload = () => {
      const width = stage.width() * 2;
      const height = stage.height() * 2;

      const canvas = document.createElement("canvas");
      canvas.width = width;
      canvas.height = height;

      const ctx = canvas.getContext("2d");
      if (!ctx) return;

      // Draw white background
      ctx.fillStyle = "white";
      ctx.fillRect(0, 0, width, height);

      ctx.drawImage(image, 0, 0, width, height);

      // Create a download link
      const finalURL = canvas.toDataURL("image/png");
      const link = document.createElement("a");
      link.download = "drawing.png";
      link.href = finalURL;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    };
  };

  const renderShapes = () => {
    return shapes.map((shape) => {
      if (shape.type === "rect") {
        return (
          <Rect
            key={shape.id}
            x={shape.x}
            y={shape.y}
            width={shape.width}
            height={shape.height}
            stroke="black"
            strokeWidth={2}
            draggable={false}
          />
        );
      } else if (shape.type === "circle") {
        return (
          <Group
            key={shape.id}
            id={`circle-${shape.id}`}
            name="circle-shape"
            draggable={false}
          >
            <Circle
              x={shape.x}
              y={shape.y}
              radius={shape.radius}
              stroke={shape.stroke || "#07a4fe"}
              strokeWidth={shape.strokeWidth || 1}
              fill={shape.fill || "transparent"}
              visible={shape.isCircleVisible !== false}
              draggable={false}
            />

            {shape.topInnerLabel &&
              shape.cornerPosition === "topRightInner" && (
                <Text
                  x={shape.x - 8}
                  y={shape.y - 8}
                  text={shape.topInnerLabel}
                  fontSize={14}
                  fill="black"
                  visible={shape.isCircleVisible !== false}
                  draggable={false}
                />
              )}

            {shape.bottomInnerLabel &&
              shape.cornerPosition === "bottomLeftInner" && (
                <Text
                  x={shape.x - 8}
                  y={shape.y - 8}
                  text={shape.bottomInnerLabel}
                  fontSize={14}
                  fill="black"
                  visible={shape.isCircleVisible !== false}
                  draggable={false}
                />
              )}

            {shape.BF_LeftLebel && shape.cornerPosition === "bottomLeft" && (
              <Text
                x={shape.x - 40}
                y={shape.y - 20}
                text={shape.BF_LeftLebel}
                fontSize={14}
                fill="black"
                visible={shape.isCircleVisible !== false}
                draggable={false}
              />
            )}

            {shape.BF_RightLabel && shape.cornerPosition === "bottomLeft" && (
              <Text
                x={shape.x + 23}
                y={shape.y - 20}
                text={shape.BF_RightLabel}
                fontSize={14}
                fill="black"
                visible={shape.isCircleVisible !== false}
                draggable={false}
              />
            )}

            {shape.TR_LeftLabel && shape.cornerPosition === "topRight" && (
              <Text
                x={shape.x - 40}
                y={shape.y + 15}
                text={shape.TR_LeftLabel}
                fontSize={14}
                fill="black"
                visible={shape.isCircleVisible !== false}
                draggable={false}
              />
            )}

            {shape.TR_RightLabel && shape.cornerPosition === "topRight" && (
              <Text
                x={shape.x + 23}
                y={shape.y + 15}
                text={shape.TR_RightLabel}
                fontSize={14}
                fill="black"
                visible={shape.isCircleVisible !== false}
                draggable={false}
              />
            )}
          </Group>
        );
      } else if (shape.type === "polyline") {
        return (
          <Line
            key={shape.id}
            id={`polyline-${shape.id}`}
            name="polyline-shape"
            points={shape.points}
            stroke={shape.stroke || "black"}
            strokeWidth={shape.strokeWidth || 2}
            tension={0}
            draggable={false}
          />
        );
      } else if (shape.type === "dash-line") {
        // Check if the shape should be conditionally visible
        const isVisible = !shape.visibleOnlyWithDashTool || showDashLines;

        return isVisible ? (
          <Line
            key={shape.id}
            points={shape.points}
            stroke={shape.stroke || "red"}
            strokeWidth={shape.strokeWidth || 2}
            dash={shape.dash}
            tension={0}
            draggable={false}
          />
        ) : null;
      } else if (shape.type === "arrow") {
        return (
          <Arrow
            key={shape.id}
            points={shape.points}
            stroke={shape.stroke}
            fill={shape.fill}
            strokeWidth={shape.strokeWidth}
            pointerLength={shape.pointerLength}
            pointerWidth={shape.pointerWidth}
            draggable={false}
          />
        );
      } else if (shape.type === "text") {
        return (
          <Text
            key={shape.id}
            x={shape.x}
            y={shape.y}
            text={shape.text}
            fontSize={shape.fontSize || 18}
            fill="black"
            draggable={false}
            onClick={() => {
              if (shape.isEditable !== false) {
                setSelectedDimension(shape);
                setCurrentDimensionValue(shape.text);
                setShowKeypad(true);
              }
            }}
            onTap={() => {
              if (shape.isEditable !== false) {
                setSelectedDimension(shape);
                setCurrentDimensionValue(shape.text);
                setShowKeypad(true);
              }
            }}
            onMouseEnter={(e) => {
              if (shape.isEditable !== false) {
                const container = stageRef.current.container();
                container.style.cursor = "pointer";
              }
            }}
            onMouseLeave={(e) => {
              const container = stageRef.current.container();
              container.style.cursor = "default";
            }}
          />
        );
      } else if (shape.type === "text_label") {
        return (
          <Text
            key={shape.id}
            x={shape.x}
            y={shape.y}
            text={shape.text}
            fontSize={shape.fontSize || 18}
            visible={shape.isVisible !== false}
            draggable={false}
          />
        );
      } else if (shape.type === "line") {
        return (
          <Line
            key={shape.id}
            points={shape.points}
            stroke={shape.stroke || "black"}
            strokeWidth={shape.strokeWidth || 2}
            tension={0}
            draggable={false}
          />
        );
      } else if (shape.type === "dimension") {
        if (shape.isVisible === false) return null;
        const [x1, y1] = shape.from;
        const [x2, y2] = shape.to;

        return (
          <Group
            key={shape.id}
            id={`dimension-${shape.id}`}
            name="dimension-group"
            draggable={false}
            onClick={() => !showToolbar && handleDimensionClick(shape.id)}
            onTap={() => !showToolbar && handleDimensionClick(shape.id)}
            onMouseEnter={(e) => {
              if (!showToolbar) {
                const container = stageRef.current.container();
                container.style.cursor = "pointer";
              }
            }}
            onMouseLeave={(e) => {
              const container = stageRef.current.container();
              container.style.cursor = "default";
            }}
          >
            <Line
              points={[x1, y1, x2, y2]}
              stroke={shape.stroke || "black"}
              strokeWidth={2}
              hitStrokeWidth={20}
              draggable={false}
            />
            {/* Add arrow heads for dimension lines */}
            {shape.isVertical ? (
              // Vertical dimension
              <>
                {/* Determine if the line is drawn from top to bottom or bottom to top */}
                {y1 <= y2 ? (
                  // Line drawn from top to bottom (y1 is above y2)
                  <>
                    {/* Top arrowhead - should point upward */}
                    <Line
                      points={[x1, y1, x1 - 6, y1 + 10]}
                      stroke={shape.stroke || "black"}
                      strokeWidth={2}
                      draggable={false}
                    />
                    <Line
                      points={[x1, y1, x1 + 6, y1 + 10]}
                      stroke={shape.stroke || "black"}
                      strokeWidth={2}
                      draggable={false}
                    />
                    {/* Bottom arrowhead - should point downward */}
                    <Line
                      points={[x1, y2, x1 - 6, y2 - 10]}
                      stroke={shape.stroke || "black"}
                      strokeWidth={2}
                      draggable={false}
                    />
                    <Line
                      points={[x1, y2, x1 + 6, y2 - 10]}
                      stroke={shape.stroke || "black"}
                      strokeWidth={2}
                      draggable={false}
                    />
                  </>
                ) : (
                  // Line drawn from bottom to top (y1 is below y2)
                  <>
                    {/* Bottom arrowhead - should point downward */}
                    <Line
                      points={[x1, y1, x1 - 6, y1 - 10]}
                      stroke={shape.stroke || "black"}
                      strokeWidth={2}
                      draggable={false}
                    />
                    <Line
                      points={[x1, y1, x1 + 6, y1 - 10]}
                      stroke={shape.stroke || "black"}
                      strokeWidth={2}
                      draggable={false}
                    />
                    {/* Top arrowhead - should point upward */}
                    <Line
                      points={[x1, y2, x1 - 6, y2 + 10]}
                      stroke={shape.stroke || "black"}
                      strokeWidth={2}
                      draggable={false}
                    />
                    <Line
                      points={[x1, y2, x1 + 6, y2 + 10]}
                      stroke={shape.stroke || "black"}
                      strokeWidth={2}
                      draggable={false}
                    />
                  </>
                )}
              </>
            ) : (
              // Horizontal dimension
              <>
                {/* Determine if the line is drawn from left to right or right to left */}
                {x1 <= x2 ? (
                  // Line drawn from left to right
                  <>
                    <Line
                      points={[x1, y1, x1 + 10, y1 - 6]}
                      stroke={shape.stroke || "black"}
                      strokeWidth={2}
                      draggable={false}
                    />
                    <Line
                      points={[x1, y1, x1 + 10, y1 + 6]}
                      stroke={shape.stroke || "black"}
                      strokeWidth={2}
                      draggable={false}
                    />
                    <Line
                      points={[x2, y1, x2 - 10, y1 - 6]}
                      stroke={shape.stroke || "black"}
                      strokeWidth={2}
                      draggable={false}
                    />
                    <Line
                      points={[x2, y1, x2 - 10, y1 + 6]}
                      stroke={shape.stroke || "black"}
                      strokeWidth={2}
                      draggable={false}
                    />
                  </>
                ) : (
                  // Line drawn from right to left
                  <>
                    <Line
                      points={[x1, y1, x1 - 10, y1 - 6]}
                      stroke={shape.stroke || "black"}
                      strokeWidth={2}
                      draggable={false}
                    />
                    <Line
                      points={[x1, y1, x1 - 10, y1 + 6]}
                      stroke={shape.stroke || "black"}
                      strokeWidth={2}
                      draggable={false}
                    />
                    <Line
                      points={[x2, y1, x2 + 10, y1 - 6]}
                      stroke={shape.stroke || "black"}
                      strokeWidth={2}
                      draggable={false}
                    />
                    <Line
                      points={[x2, y1, x2 + 10, y1 + 6]}
                      stroke={shape.stroke || "black"}
                      strokeWidth={2}
                      draggable={false}
                    />
                  </>
                )}
              </>
            )}
            <Text
              x={
                shape.isVertical
                  ? x1 -
                    (shape.textOffset || 30) -
                    (shape.text.toString().length > 3
                      ? (shape.text.toString().length - 3) * 8
                      : 0)
                  : (x1 + x2) / 2 - 15
              }
              y={
                shape.isVertical
                  ? (y1 + y2) / 2 - 10
                  : shape.textPosition === "top"
                  ? y1 - 25
                  : y1 + 10
              }
              text={shape.text.toString()}
              fontSize={16}
              fill="black"
              draggable={false}
            />
          </Group>
        );
      } else if (shape.type === "lineWithLabel") {
        return (
          <Group key={shape.id}>
            <Line
              points={[...shape.from, ...shape.to]}
              stroke={shape.stroke || "black"}
              strokeWidth={2}
            />
            {/* ค่าที่ผู้ใช้กรอก */}
            <Text
              x={shape.textMainPos?.x ?? (shape.from[0] + shape.to[0]) / 2 - 10}
              y={shape.textMainPos?.y ?? (shape.from[1] + shape.to[1]) / 2 - 20}
              text={shape.textMain?.toString()}
              fontSize={14}
              fill="black"
            />
            {/* ค่าความต่าง */}
            <Text
              x={shape.textDiffPos?.x ?? (shape.from[0] + shape.to[0]) / 2 - 10}
              y={shape.textDiffPos?.y ?? (shape.from[1] + shape.to[1]) / 2}
              text={shape.textDiff?.toString()}
              fontSize={14}
              fill="black"
            />
          </Group>
        );
      } else if (shape.type === "tickLine") {
        return (
          <Line
            key={shape.id}
            points={[...shape.from, ...shape.to]}
            stroke={shape.stroke || "black"}
            strokeWidth={2}
          />
        );
      }

      return null;
    });
  };

  // Add an effect to ensure shapes are properly rendered when dimensions change
  useEffect(() => {
    if (stageRef.current) {
      const layer = stageRef.current.findOne("Layer");
      if (layer) {
        layer.batchDraw();
      }
    }
  }, [dimensions, showToolbar]);

  // Add or update this function to handle keypad confirmation for text elements
  const handleKeypadConfirm = (value) => {
    if (selectedDimension) {
      // Convert to number if possible
      const numericValue = !isNaN(parseFloat(value))
        ? parseFloat(value)
        : value;

      if (selectedPattern === "Square and Round Holes") {
        // Update dimensionValues based on which dimension is being edited
        setDimensionValues((prev) => {
          if (!selectedDimension.isVertical) {
            // Horizontal dimension = width/糸幅
            return { ...prev, width: numericValue.toString() };
          } else {
            // Vertical dimension = height/長さ
            return { ...prev, height: numericValue.toString() };
          }
        });
      }

      if (selectedDimension.type === "text") {
        // For Pattern3 text elements
        if (
          selectedDimension.dimensionId &&
          selectedDimension.dimensionId.startsWith("pattern3")
        ) {
          let updatedShapes = updatePatternThree(
            shapes,
            selectedDimension.id,
            numericValue
          );
          updatedShapes = centerPatternThree(updatedShapes, stageRef);
          setShapes(updatedShapes);
        } else {
          // For regular text elements
          setShapes((prevShapes) =>
            prevShapes.map((shape) => {
              if (shape.id === selectedDimension.id) {
                return {
                  ...shape,
                  text: numericValue.toString(),
                  fontSize: shape.fontSize || 18,
                };
              }
              return shape;
            })
          );
        }
      } else {
        // Handle other dimension types (existing code)
        updateDimensionValue(selectedDimension.id, numericValue);
      }
    }

    // Hide the keypad after confirmation
    setShowKeypad(false);
    setCurrentDimensionValue("");
    setSelectedDimension(null);
  };

  // Reset dimension values when pattern changes
  useEffect(() => {
    if (selectedPattern !== "Square and Round Holes") {
      setDimensionValues({ width: "", height: "" });
    }
  }, [selectedPattern]);

  useEffect(() => {
    if (!stageRef.current) return;
    const stage = stageRef.current;

    switch (activeTool) {
      case "line":
      case "dashed":
      case "dotDash":
      case "dotDash2":
        stage.container().style.cursor = "crosshair";
        break;
      case "selection":
        stage.container().style.cursor = "default"; // หรือ pointer ก็ได้
        break;
      default:
        stage.container().style.cursor = "default";
        break;
    }
  }, [activeTool]);

  return (
    <FullscreenProvider>
      <div className="flex flex-col w-full h-screen overflow-hidden">
        <header className="w-full bg-green-600 text-white fixed top-0 z-50 flex items-center px-4">
          <div className="flex items-center justify-between w-full">
            {/* Left Side */}
            <div className="space-x-5">
              <button type="button" className="hover:bg-green-700 p-2 rounded">
                <FaXmark
                  className="text-white w-6 h-6 sm:w-8 sm:h-8"
                  onClick={handleClose}
                />
              </button>

              <button
                type="button"
                onClick={handleSaveImage}
                disabled={!showToolbar}
                className={`p-2 rounded ${
                  !showToolbar
                    ? "opacity-50 cursor-not-allowed"
                    : "hover:bg-green-700"
                }`}
                title={
                  !showToolbar ? "Save is available only during adjustment" : ""
                }
              >
                <FaSave className="text-white w-6 h-6 sm:w-8 sm:h-8" />
              </button>
            </div>

            {/* Center Side */}
            <div>
              <div
                className="flex items-center cursor-pointer"
                onClick={() => setShowProjectInfo(!showProjectInfo)}
              >
                <div className="transform transition-transform duration-200">
                  {showProjectInfo ? (
                    <VscTriangleRight className="w-6 h-6 sm:w-8 sm:h-8 transform rotate-90" />
                  ) : (
                    <VscTriangleRight className="w-6 h-6 sm:w-8 sm:h-8" />
                  )}
                </div>
                <h1 className="text-lg sm:text-xl font-bold">Draw Page</h1>
              </div>

              {showProjectInfo && (
                <div className="absolute left-0 mt-5 bg-white rounded-md shadow-lg z-50 w-[500px]">
                  <div className="p-3 border-b border-gray-200 font-bold text-black">
                    <span className="text-lg">{t("DrawPage.projectNo")}</span>
                  </div>
                  <table className="w-full text-center text-black">
                    <thead className="bg-gray-300 border-2 border-white">
                      <tr>
                        <th className="p-2 border-2 border-white">
                          {t("DrawPage.threadWidth")}
                        </th>
                        <th className="p-2 border-2 border-white">
                          {t("DrawPage.length")}
                        </th>
                        <th className="p-2 border-2 border-white">
                          {t("DrawPage.quantity")}
                        </th>
                        <th className="p-2 border-2 border-white">
                          {t("DrawPage.unitPrice")}
                        </th>
                        <th className="p-2 border-2 border-white">
                          {t("DrawPage.amount")}
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="bg-gray-300 border-2 border-white h-16">
                        <td className="p-2 border-2 border-white align-bottom">
                          <div className="h-full flex items-end justify-center">
                            <span>-</span>
                          </div>
                        </td>
                        <td className="p-2 border-2 border-white align-bottom">
                          <div className="h-full flex items-end justify-center">
                            <span>-</span>
                          </div>
                        </td>
                        <td className="p-2 border-2 border-white">
                          <div className="h-16 flex flex-col justify-between items-center">
                            <span>No.</span>
                            <span className="text-sm">-</span>
                          </div>
                        </td>
                        <td className="p-2 border-2 border-white align-bottom">
                          <div className="h-full flex items-end justify-center">
                            <span>-</span>
                          </div>
                        </td>
                        <td className="p-2 border-2 border-white align-bottom">
                          <div className="h-full flex items-end justify-center">
                            <span>-</span>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              )}
            </div>

            {/* Right Side */}
            <div className="space-x-5 py-2">
              {/* <LanguageSelector /> */}

              <button
                type="button"
                onClick={() => {
                  if (!selectedPattern && !showToolbar) {
                    togglePopup();
                  }
                }}
                disabled={showToolbar || !!selectedPattern}
                className={`p-2 rounded ${
                  showToolbar || selectedPattern
                    ? "opacity-50 cursor-not-allowed"
                    : "hover:bg-green-700 cursor-pointer"
                }`}
                title={
                  showToolbar
                    ? "Disabled during adjustment"
                    : selectedPattern
                    ? "Cannot create new pattern while one already exists"
                    : ""
                }
              >
                <TbLetterL className="text-white w-8 h-8" />
              </button>

              <button type="button" className="hover:bg-green-700 p-2 rounded">
                <ImUndo onClick={undo} className="text-white w-8 h-8" />
              </button>

              <button type="button" className="hover:bg-green-700 p-2 rounded">
                <SlOptionsVertical className="text-white w-8 h-8" />
              </button>
            </div>
          </div>
        </header>

        {showPopup && (
          <div className="fixed inset-0 bg-gray-400 bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-green-600 p-6 rounded-3xl shadow-lg max-w-md w-full">
              <div className="space-y-3">
                {patternKeys.map((key, index) => {
                  const isDisabled = disabledKeys.includes(key);

                  return (
                    <div
                      key={index}
                      className={`bg-opacity-20 rounded-lg ${
                        isDisabled
                          ? "cursor-not-allowed bg-gray-700"
                          : "hover:bg-slate-200"
                      }`}
                    >
                      <button
                        className={`flex w-full p-2 gap-x-3 items-center group ${
                          isDisabled ? "opacity-50 pointer-events-none" : ""
                        }`}
                        onClick={() => {
                          if (!isDisabled) handlePatternSelection(key);
                        }}
                        disabled={isDisabled}
                      >
                        {iconMap[key]}
                        <span
                          className={`text-lg font-semibold ${
                            isDisabled
                              ? "text-gray-300"
                              : "text-white group-hover:text-black"
                          }`}
                        >
                          {t(`DrawPage.${key}`, { lng: "jp" })}
                        </span>
                      </button>
                      {index < 6 && <hr className="border-white opacity-50" />}
                    </div>
                  );
                })}
                <div className="bg-opacity-20 rounded-lg hover:bg-slate-200">
                  <button
                    onClick={handleSquareAndRoundHoles}
                    className="flex w-full p-2 gap-x-3 items-center group"
                  >
                    <div className="relative">
                      <FaRegSquare className="text-white text-2xl group-hover:text-black" />
                      <FaRegCircle className="absolute inset-0 m-auto text-[0.8rem] font-bold text-white group-hover:text-black" />
                    </div>
                    <span className="text-white text-lg font-semibold group-hover:text-black">
                      {t("DrawPage.squareAndRoundHoles", { lng: "jp" })}
                    </span>
                  </button>
                </div>
                <div className="flex justify-center mt-4">
                  <button
                    onClick={handleCancel}
                    className="w-full py-2 bg-gray-300 text-black rounded-xl hover:bg-gray-400"
                  >
                    {t("DrawPage.cancel", { lng: "jp" })}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Keypad - render at the top level with high z-index */}
        {showKeypad && (
          <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-30">
            <NumericKeypad
              initialValue={
                currentDimensionValue ||
                selectedDimension?.text?.toString() ||
                ""
              }
              onConfirm={(value) => {
                handleKeypadConfirm(value);
              }}
              onCancel={() => {
                setShowKeypad(false);
                setCurrentDimensionValue("");
                setSelectedDimension(null);
              }}
            />
          </div>
        )}

        <div
          ref={containerRef}
          className="flex-1 relative overflow-hidden"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20'%3E%3Crect width='20' height='20' fill='white'/%3E%3Cpath d='M 20 0 L 0 0 0 20' stroke='lightgray' stroke-width='1.2' fill='none'/%3E%3C/svg%3E")`,
            backgroundRepeat: "repeat",
          }}
        >
          {/* Render either the adjust section or the full toolbar */}
          <div className="mx-3 relative">
            <ToolBar
              stageRef={stageRef}
              shapes={shapes}
              showToolbar={showToolbar}
              showAdjustSection={showAdjustSection}
              toggleToolbar={toggleToolbar}
              selectedPattern={selectedPattern}
              dimensionValues={dimensionValues}
              onRoundHoleSubmit={handleRoundHoleSubmit}
            />
          </div>

          {/* Drawing area */}
          <Stage
            width={dimensions.width}
            height={dimensions.height}
            ref={stageRef}
          >
            <Layer>{renderShapes()}</Layer>
          </Stage>
          <DimensionTool
            stageRef={stageRef}
            shapes={shapes}
            setShapes={setShapes}
            showToolbar={showToolbar}
          />
          <FullScreenTool stageRef={stageRef} />
        </div>
      </div>
    </FullscreenProvider>
  );
}

export default DrawPage;
